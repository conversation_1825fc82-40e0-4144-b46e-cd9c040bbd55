#!/bin/bash

# ERMAC Local Testing Script
# Lightweight version for testing on low-performance laptops

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

function echo_info {
    echo -e "${BLUE}[INFO]${NC} $1"
}

function echo_success {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

function echo_warning {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

function echo_error {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo_info "Starting ERMAC Local Testing Environment..."
echo_warning "This is a lightweight version for testing purposes only!"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo_error "Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create local environment file
echo_info "Creating local environment configuration..."

cat > .env.local << EOF
BACKEND_INTERFACE=0.0.0.0
BACKEND_PORT=8089
BACKEND_URL=http://localhost:8089/
BACKEND_DEBUG=true

FRONTEND_URL=http://localhost
FRONTEND_INTERFACE=0.0.0.0
FRONTEND_PORT=3000

MYSQL_PORT=3306
MYSQL_DATABASE=ermac_local
MYSQL_USER=ermac_user
MYSQL_ROOT_PASSWORD=root123
MYSQL_PASSWORD=user123
MYSQL_HOST=mysql

PHP_SOCKETS_PORT=8000

TIMEZONE=UTC
EOF

# Create lightweight docker-compose for local testing
echo_info "Creating local Docker Compose configuration..."

cat > docker-compose.local.yml << 'EOF'
version: '3.5'

services:
  mysql:
    image: mysql:8.0
    container_name: ermac_local_mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ermac_local
      MYSQL_USER: ermac_user
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_PASSWORD: user123
      MYSQL_ROOT_HOST: '%'
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password

  php:
    build:
      context: .
      target: php
    container_name: ermac_local_php
    restart: unless-stopped
    depends_on:
      - mysql
    volumes:
      - ./backend/:/var/www:rw
    working_dir: /var/www
    ports:
      - "8089:8000"
    environment:
      DB_HOST: mysql
      DB_PORT: 3306
      DB_DATABASE: ermac_local
      DB_USERNAME: ermac_user
      DB_PASSWORD: user123
      DB_TIMEZONE: UTC
      APP_TIMEZONE: UTC
      APP_URL: http://localhost:8089/
      APP_DEBUG: true

volumes:
  mysql_data:
EOF

# Setup backend environment
echo_info "Setting up backend for local testing..."
if [[ ! -f backend/.env ]]; then
    cp backend/.env.example backend/.env
fi

# Configure backend for local testing
sed -i 's/DB_HOST=.*/DB_HOST=mysql/' backend/.env
sed -i 's/DB_DATABASE=.*/DB_DATABASE=ermac_local/' backend/.env
sed -i 's/DB_USERNAME=.*/DB_USERNAME=ermac_user/' backend/.env
sed -i 's/DB_PASSWORD=.*/DB_PASSWORD=user123/' backend/.env
sed -i 's/APP_URL=.*/APP_URL=http:\/\/localhost:8089\//' backend/.env
sed -i 's/APP_DEBUG=.*/APP_DEBUG=true/' backend/.env

# Setup frontend for local development
echo_info "Setting up frontend for local testing..."
if [[ ! -f frontend/.env ]]; then
    cp frontend/.env.example frontend/.env
fi

# Configure frontend for local testing
sed -i 's/REACT_APP_BACKEND_URL=.*/REACT_APP_BACKEND_URL=http:\/\/localhost:8089\//' frontend/.env
sed -i 's/REACT_APP_TIMEZONE=.*/REACT_APP_TIMEZONE=UTC/' frontend/.env

echo_info "Starting local testing environment..."

# Start only essential services (MySQL + PHP Backend)
docker-compose -f docker-compose.local.yml up -d mysql

echo_info "Waiting for MySQL to start..."
sleep 15

docker-compose -f docker-compose.local.yml up -d php

echo_info "Waiting for PHP backend to start..."
sleep 10

# Install backend dependencies
echo_info "Installing backend dependencies..."
docker-compose -f docker-compose.local.yml exec -T php composer install

# Setup database
echo_info "Setting up database..."
docker-compose -f docker-compose.local.yml exec -T php php artisan migrate --force
docker-compose -f docker-compose.local.yml exec -T php php artisan db:seed --force

# Generate keys
echo_info "Generating application keys..."
docker-compose -f docker-compose.local.yml exec -T php php artisan jwt:secret --force
docker-compose -f docker-compose.local.yml exec -T php php artisan key:generate --force

echo_success "Backend is ready!"

# Start frontend in development mode (outside Docker for better performance)
echo_info "Starting frontend development server..."
echo_warning "Frontend will run outside Docker for better performance on low-end laptops"

cd frontend

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo_error "Node.js is not installed. Please install Node.js first."
    echo_info "You can install Node.js from: https://nodejs.org/"
    exit 1
fi

# Install frontend dependencies
echo_info "Installing frontend dependencies..."
npm install

# Start development server
echo_info "Starting React development server..."
echo_warning "This will start the frontend on http://localhost:3000"
echo_info "Backend API is available at http://localhost:8089"

# Create start script for convenience
cat > ../start_local_frontend.sh << 'EOF'
#!/bin/bash
cd frontend
npm start
EOF

chmod +x ../start_local_frontend.sh

echo ""
echo_success "🎉 Local testing environment is ready!"
echo ""
echo_info "📋 Local Testing URLs:"
echo "   Frontend: http://localhost:3000 (React Dev Server)"
echo "   Backend API: http://localhost:8089"
echo "   MySQL: localhost:3306"
echo ""
echo_info "🔑 Database Credentials:"
echo "   Database: ermac_local"
echo "   Username: ermac_user"
echo "   Password: user123"
echo "   Root Password: root123"
echo ""
echo_info "🔑 Default Panel Login:"
echo "   Username: root"
echo "   Password: changemeplease"
echo ""
echo_info "🚀 To start frontend:"
echo "   cd frontend && npm start"
echo "   OR run: ./start_local_frontend.sh"
echo ""
echo_warning "⚠️  Note: This is for testing only!"
echo "   - Real-time features disabled"
echo "   - No builder service"
echo "   - Development mode enabled"
echo ""
echo_success "Happy testing! 🧪"
