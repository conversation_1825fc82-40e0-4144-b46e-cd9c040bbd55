{"translations": {"AUTH_TOKEN": "Token", "AUTH_CODEWORD": "Codeword", "BUILDER": "Builder", "HVNC_SERVER": "HVNC", "ANDROID_NAME": "Android", "AUTH_TOKEN_PLACEHOLDER": "Your private token", "AUTH_CODEWORD_PLACEHOLDER": "Codeword", "AUTH_SIGN_IN": "Enter", "CHANGE_LANGUAGE": "Select language", "PAGE_TITLE_BOTS": "<PERSON><PERSON>", "DELETE_SELECTED_BOTS": "Delete selected bots?", "UNDONE_ACTION": "This actions can't be undone", "CLEAR_SELECTION": "Clear selection", "FILTERS_BTN": "Filters", "BOTS_NOT_FOUND_TITLE": "Bots not found :/", "BOTS_NOT_FOUND_DESCRIPTION": "Sorry, the bots you searched does not exist.", "BOT_ONLINE": "online", "BOT_OFFLINE:": "offline", "BOT_REMOVED_APP": "uninstalled", "BOT_LAST_ONLINE": "Last online", "BOT_INFECTED_AT": "Infected at", "BOT_LAST_ONLINE_NO_DATA": "No data", "BOT_PERMISSIONS": "Permissions", "BOT_LOGS": "Logs", "BOT_INJECTIONS": "Injections", "SIDEBAR_NAV_BOTS": "<PERSON><PERSON>", "SIDEBAR_NAV_BANKS": "Banks", "SIDEBAR_NAV_CARDS": "Cards", "SIDEBAR_NAV_CRYPT": "Crypt", "SIDEBAR_NAV_WALLET": "Wallet", "SIDEBAR_NAV_STEALER": "Stealers", "SIDEBAR_NAV_EMAIL": "Email", "SIDEBAR_NAV_SHOPS": "Shops", "SIDEBAR_NAV_INJECTIONS": "Injections", "SIDEBAR_NAV_SMART_INJECTIONS": "Smart injections", "SIDEBAR_NAV_STATS": "General stats", "SIDEBAR_NAV_USERS": "Users", "SIDEBAR_NAV_FILE": "File manager", "SIDEBAR_NAV_THEME": "Theme", "SIDEBAR_PANEL_DESC": "android bots panel", "THEME_LIGHT": "Light", "THEME_DARK": "Dark", "HEADER_SEARCH_PLACEHOLDER": "Search all logs...", "ONLINE_SEC_AGO": "sec. ago", "ONLINE_MIN_AGO": "min. ago", "ONLINE_HRS_AGO": "hours ago", "ONLINE_DAYS_AGO": "days ago", "BOTS_TABLE_DEVICE": "Device info", "BOTS_TABLE_PERMISSIONS": "Permissions", "BOTS_TABLE_STATUS": "Online status", "BOTS_TABLE_INJECTIONS": "Injections", "BOTS_TABLE_LOGS": "Logs", "BOTS_TABLE_ACTIONS": "Actions", "LOGS_ACCOUNTS": "Show accounts", "LOGS_KEYLOGGER": "Keylogger", "LOGS_GMAIL": "Show Gmail messages", "LOGS_GMAIL_TITLES": "Show Gmail titles", "BOT_GEOLOCATION": "Geolocation / IP", "PERMISSION_ACCESSIBILITY": "Accessibility", "PERMISSION_MODULE_LOADED": "Module loaded", "PERMISSION_SMS": "SMS list", "PERMISSION_KEYLOGGER": "Keylogger", "BOT_ACTION_DELETE_TITLE": "Delete bot?", "MODAL_BUTTON_CANCEL": "Cancel", "BOT_ACTION_DELETE_CONFIRM": "Delete", "BOT_FC": "First connection", "BOT_LC": "Last connection", "USER_LOGOUT": "Logout", "USER_LOGOUT_TITLE": "Confirm", "USER_LOGOUT_DESC": "Do you want to exit?", "USER_LOGOUT_CONFIRM": "Logout", "ACTION_DELETE": "Delete", "ACTION_DELETE_TITLE": "Delete log", "ACTION_EDIT": "Edit", "ACTION_EDIT_TITLE": "Edit log", "LOG_COLUMN_BOT_ID": "Bot ID", "LOG_COLUMN_APP": "Application", "LOG_COLUMN_LOGIN": "<PERSON><PERSON>", "LOG_COLUMN_EMAIL": "Email", "LOG_COLUMN_PASSWORD": "Password", "LOG_COLUMN_NAME": "Name", "LOG_COLUMN_SURNAME": "Surname", "LOG_COLUMN_BACKUP": "Backup phrase", "LOG_COLUMN_CARD_NUMBER": "Card number", "LOG_COLUMN_HOLDER": "Name", "LOG_COLUMN_LAST_NAME": "Last name", "LOG_COLUMN_EXP": "MM/YY", "LOG_COLUMN_CVV": "CVV", "LOG_COLUMN_ADDITIONAL": "Additional", "LOG_COLUMN_SEED": "Seed phrase", "LOG_COLUMN_CREATED": "Date", "SEARCH_PLACEHOLDER": "Search", "INJ_COLUMN_APP": "Application", "INJ_COLUMN_NAME": "Name", "INJ_COLUMN_TYPE": "Type", "INJ_COLUMN_AUTO": "AutoInject", "INJ_COLUMN_HTML": "HTML", "INJ_COLUMN_ACTIONS": "Actions", "ON": "On", "OFF": "Off", "NEW_INJECT": "New inject", "USER_COLUMN_TOKEN": "Token", "USER_COLUMN_NAME": "Name", "USER_COLUMN_ROLE": "Role", "USER_COLUMN_TAG": "Tag", "USER_COLUMN_EMAIL": "Email", "USER_COLUMN_DATE": "Date", "USER_COLUMN_EXP": "Expiration date", "USER_COLUMN_ACTIONS": "Actions", "STATS_COLUMN_COUNTRY": "Country", "STATS_COLUMN_COUNT": "Count", "STATS_BOTS_TOTAL": "Total bots", "STATS_BOTS_ONLINE": "Online bots", "STATS_BOTS_OFFLINE": "Offline bots", "STATS_BOTS_REMOVED": "Uninstalled bots", "STATS_BOTS_WO_PERMS": "<PERSON><PERSON> without permissions", "STATS_BOTS_PERMS": "Bots with permissions", "STATS_INJECTS_TIMELINE": "Injects stats timeline", "STATS_LOGS_TIMELINE": "Logs stats timeline", "STATS_INJECTS": "Injects stats", "STATS_LOGS": "Logs stats", "STATS_TOTAL": "Total", "LOGS_FILTER_TITLE": "Logs filter", "SELECT_APP": "Select application", "GET_ACCOUNTS": "Get accounts", "GET_INSTALLED_APPS": "Get installed apps", "SEND_SMS": "Send SMS", "UPDATE_INJECTS_LIST": "Update injects list", "RUN_APP": "Run app", "START_APP": "Start app", "SEND_PUSH": "Send push", "DELETE_APP": "Delete app", "GMAIL_TITLES": "Gmail titles", "GET_GMAIL_MESSAGE": "Get Gmail message", "COMMANDS": "Commands", "BOTS_FILTER": "Bots filter", "COUNTRY": "Country", "INJECTIONS": "Injections", "STATUS": "Status", "TAGS": "Tags", "TYPES": "Types", "SEARCH": "Search", "CREATED": "Created", "CLOSE": "Close", "404_TITLE": "404 - Page not found :/", "404_SUBTITLE": "Sorry, the page you've looking for doesn't exist.", "SHOW_ALL_BOTS": "Show all bots", "INJECTION_DATA": "Injection data", "DATA": "Data", "LOG_COMMENT": "Log comment", "COMMENT_TEXT": "Comment text...", "SAVE_COMMENT": "Save comment", "COMMENT": "Comment", "REFRESH": "Refresh", "MESSAGE_NUMBER": " Message number", "EMPTY_FIELD_ERROR": "This field can't be empty", "LOADING": "Loading", "TABLE_EMPTY": "Data not found", "NEVER": "Never", "TOKEN": "Token", "NAME": "Name", "NEW_PASSWORD": "New password", "PASSWORD": "Password", "ROLE": "Role", "EMAIL": "Email", "ADD_USER": "Add user", "EXPIRATION_DATE": "Expiration date", "DELETE_USER_TITLE": "Delete user?", "ACTIVATE_USER_TITLE": "Activate user?", "SUSPEND_USER_TITLE": "Suspend user?", "ACTIVATE_USER_DESC": "This action will activate user account", "SUSPEND_USER_DESC": "This action will suspend user account", "NEW_USER": "New user", "USER_MAILING": "Mailing", "USER_MAILING_TEXT": "Mailing text", "SEND": "Send", "UNBIND_TELEGRAM": "Unbind telegram", "APPLICATION": "Application", "UPDATED_AT": "Updated", "CREATED_AT": "Created", "ACTION": "Action", "FIND": "Find", "CHECK_ALL": "Check all", "UNCHECK_ALL": "Uncheck all", "EDIT_USER": "Edit user", "GENERATE_UUID": "Generate UUID", "GENERATE_UUID_MESSAGE": "Generating UUID token", "GENERATE_UUID_DESC": "Invite token successfuly generated", "BOT_INFO": "Bot info", "OPERATION_SYSTEM": "Operating System", "BATTERY_LEVEL": "Battery", "SIM_CARDS": "Sim cards", "SIM_CARD": "<PERSON>m", "SIM_NOT_PLUGGED": "Not plugged", "COMMAND_HISTORY": "Command history", "USER": "User", "COMMAND": "Command", "PAYLOAD": "Payload", "DATE": "Date", "COMMON_INFORMATION": "Common information", "INJECT_ADD": "Add new inject", "INJECT_NAME": "Inject name", "INJECT_APP_NAME": "Application name", "INJECT_TYPE": "Type", "INJECT_ICON": "Icon", "INJECT_HTML": "Inject HTML Template", "INJECT_SAVE": "Save inject", "INJECT_EDIT": "Edit inject", "INJECT_DELETE": "Delete injection?", "UPLOAD": "Click to upload", "PERMISSION_NAME": "Permission name", "AUTO_COMMANDS": "Auto commands", "OPEN_INJECT": "Open inject", "SEND_PUSH_TITLE": "Push title", "SEND_PUSH_TEXT": "Push text", "NUMBER": "Number", "TYPE_MESSAGE": "Type message", "GET_SEED_PHRASE": "Get SEED phrase", "SEED_PHRASE": "SEED phrase", "SAVE_AC": "Save auto commands", "bots.list": "View bots list", "bots.delete": "Delete bots", "bots.comment": "Comment bots", "users.list": "View users list", "users.create": "Create users", "users.createRoot": "Create ROOT user", "users.createAdmin": "Create ADMIN user", "users.createSeo": "Create SEO user", "users.createUser": "Create user", "users.edit": "Edit user", "users.delete": "Delete user", "users.token": "View user token", "banks.list": "View banks list", "banks.delete": "Delete banks", "credit_cards.list": "View credit cards list", "credit_cards.delete": "Delete credit cards", "stealers.list": "View stealers list", "stealers.delete": "Delete stealers list", "injections.list": "View injections list", "injections.create": "Create injections", "injections.edit": "Edit injections", "injections.delete": "Delete injections", "crypt.list": "View crypt list", "crypt.delete": "Delete crypt list", "shops.list": "View shops list", "shops.delete": "Delete shops", "emails.list": "View emails list", "emails.delete": "Delete emails", "wallets.list": "View wallets list", "wallets.delete": "Delete wallets", "permissions.list": "View permissions list", "permissions.change": "Change permissions", "stats.list": "View stats", "sms.list": "View SMS list", "events.list": "View events list", "googleauth.list": "View Google Auth list", "hidesms.list": "View HideSMS list", "keylogger.list": "View keyelogger logs", "mail.list": "View mail list", "otheraccounts.list": "View accounts list", "phonenumber.list": "View phone numbers list", "pushlist.list": "View PUSH list", "ussd.list": "View USSD list", "banks.editComment": "Comment", "credit_cards.editComment": "Comment", "stealers.editComment": "Comment", "crypt.editComment": "Comment", "shops.editComment": "Comment", "emails.editComment": "Comment", "wallets.editComment": "Comment", "applist.list": "View installed apps list", "smslist.list": "View SMS list", "bots": "<PERSON><PERSON>", "users": "Users", "banks": "Banks", "credit_cards": "Credit cards", "stealers": "Stealers", "injections": "Injections", "crypt": "Crypt", "shops": "Shops", "emails": "Emails", "wallets": "Wallets", "permissions": "Permissions", "stats": "Stats", "sms": "SMS", "events": "Events", "googleauth": "Google Auth", "hidesms": "HideSMS", "keylogger": "Keylogger", "mail": "Mail", "otheraccounts": "Accounts", "phonenumber": "Phone number", "pushlist": "PUSH list", "ussd": "USSD", "applist": "Installed applications", "smslist": "SMS list", "USER_PERMISSIONS": "User permissions", "SAVE": "Save", "SEARCH_RESULT_TITLE": "Results by search query", "LOG_TYPE": "Log type", "LOG": "Log", "BOT_ID_APP": "Bot ID / App", "SEARCH_NO_RESULTS": "No results were found for your query", "SEARCH_NO_RESULTS_SUBTITLE": "Try to change search query", "SEARCH_NO_RESULTS_EXTRA": "Change search query", "SEARCHING": "Searching for", "SEARCHING_SUBTITLE": "It may take a few seconds", "FISHING": "Fishing", "DELETE": "Delete", "SEND_TO_ALL": "Send to all", "SELECT_SIM": "Select SIM-card", "EMPTY_FIELD": "Field can`t be empty", "SITE_URL": "Site URL", "ACTIVE_ONLY": "Active only", "ALL": "All", "TYPE": "Type", "LOGS_LOADED": "Logs loaded", "SUCCESSFUL": "Successful", "OPTIMIZE_LIST": "Optimize list", "CONFIRM_TITLE": "You sure?", "OPTIMIZE_LIST_MESSAGE": "This action will delete all bots that have been inactive for more than 40 hours", "SHOW_INJECT_HTML": "Show HTML", "AUTO_INJECT": "Auto inject", "LOGS_NOT_FOUND_TITLE": "Logs not found :/", "LOGS_NOT_FOUND_DESCRIPTION": "Someday they'll be here", "NO_ACCESSIBILITY_BOTS": "No accessibility bots", "SELECT_ALL": "Select all", "STATS_ONLINE": "Online", "STATS_OFFLINE": "Offline", "STATS_NEW_LAST_HOUR": "New", "STATS_BOTS": "Bots statistic", "STATS_DEAD": "Dead", "STATS_NO_PERMISSIONS": "No permissions", "STATS_DELETED_APP": "Deleted app", "APPLY": "Apply", "STATS_NEW_BOTS_PAST_HOUR": "Past hour", "STATS_NEW_BOTS_PAST_DAY": "Past day", "STATS_NEW_BOTS_PAST_WEEK": "Past week", "STATS_NEW_BOTS_PAST_MONTH": "Past month", "STATS_NEW_BOTS": "New bots", "STATS_ETC": "Etc.", "LOGS": "Logs", "MOST_INJECTED_COUNTRIES": "Most injected countries", "STATS_WORLDMAP": "World stats", "SUMMARY_BOTS_INJECTS_STATS": "Injects  & logs summary stats", "INJECTS_LOGS": "Injects logs", "WAITING_FOR_DATA": "Waiting for data", "SMART_INJECT_ACTION": "Action", "INJECT_OPENED": "Opened", "INFORMATION": "Information", "LAST_ACTIVITY": "Last activity", "SMART_INJECT_STATE": "State", "ACTION_DEFAULT": "<PERSON><PERSON><PERSON>", "ACTION_LOADING": "Loading", "ACTION_CODE": "Code", "ACTION_SUCCESS": "Success", "BOT_START_FTP_SESSION": "File manager", "SIDEBAR_NAV_FISHING": "Fishing", "BOT_SUB_INFO": "Sub info", "NOT_CONNECTED": "Not connected", "CONNECTED": "Connected", "UPDATE": "Update", "SEARCH_SELECT_BOT": "Search / select bot", "SELECT_BOT": "Select bot", "SELECT_BOT_DESCRIPTION": "Please, select bot to view phone file system", "PATH": "Path", "SIZE": "Size", "GET_CALL_HISTORY_COMMAND": "Call history", "GET_CONTACTS_COMMAND": "Get contacts", "ADD_CONTACT_COMMAND": "Add contact", "GET_LOCATION_COMMAND": "Get location", "GET_IMAGES_COMMAND": "Get images", "OPEN_APP_COMMAND": "Open APP", "OPEN_WHATSAPP_COMMAND": "Send WhatsApp message", "MAKE_CALL_COMMAND": "Make Call", "FORWARD_SMS_COMMAND": "Forward SMS", "SEND_SMS_COMMAND": "Send SMS", "START_USSD_COMMAND": "Start USSD", "FORWARD_CALL_COMMAND": "Forward call", "PUSH_COMMAND": "<PERSON><PERSON>", "GET_ACCOUNTS_COMMAND": "Get accounts", "GET_INSTALL_APPS_COMMAND": "Get installed apps", "GET_SMS_COMMAND": "Get SMS", "REQUEST_DEFAULT_SMS_COMMAND": "Set as Default SMS App", "START_INJECT_COMMAND": "Start inject", "UPDATE_INJECT_AND_LIST_APPS_COMMAND": "Update injects list", "OPEN_URL_COMMAND": "Open URL", "START_APP_COMMAND": "Start App", "CALLING_COMMAND": "Calling", "DELETE_APPLICATION_COMMAND": "Delete app", "GMAIL_TITLES_COMMAND": "Gmail titles", "GET_GMAIL_MESSAGE_COMMAND": "Get Gmail message", "START_ADMIN_COMMAND": "Start admin", "TAKE_SCREENSHOT_COMMAND": "Make screenshot", "CLEAR_CACHE_COMMAND": "Clear cache", "START_AUTHENTICATOR2_COMMAND": "Start authentication", "GET_SEED_COMMAND": "Get SEED phrase", "KILL_ME_COMMAND": "Kill bot", "DOWNLOADED_FILES": "Downloaded files", "VNC": "VNC (Virtual Network Computing)", "SELECTED_BOT": "Selected bot", "VNC_CONTROLS": "VNC controls", "VNC_VIEWPORT": "VNC viewport", "VNC_SELECT_BOT_HERE": "No selected bot, you can select bot here ->", "VNC_CLICK_HERE": "And click this button", "HERE": "Here", "SCROLL": "<PERSON><PERSON>", "SWIPE": "Swipe", "VNC_START": "VNC start", "VNC_STOP": "VNC stop", "VNC_TAP": "Tap", "VNC_LONG_PRESS": "Long press", "VNC_CUT_TEXT": "Cut text", "VNC_CLICK_AT_TEXT": " Click at text", "VNC_CLICK_AT_CONTAIN_TEXT": "Click at contain text", "VCN_SWIPE": "Swipe", "VNC_CLICK_AT": "Click at", "VNC_SCROLL_UP": "Scroll up", "VNC_SCROLL_DOWN": "Scroll down", "VNC_SWIPE_UP": "Swipe up", "VNC_SWIPE_LEFT": "<PERSON>wipe left", "VNC_SWIPE_DOWN": "Swipe down", "VNC_SWIPE_RIGHT": "Swipe right", "VNC_CONTROLS_HINT": "Press Right-Click to see button description", "SELECT_BOT_AND_START_VNC": "Select bot and start VNC", "NO_INJECTIONS": "No injections", "MESSAGE": "Message", "METHOD": "Method", "LOCK_SCREEN": "Lock screen", "YES": "Yes", "NO": "No", "USSD": "USSD command", "GET": "Get", "SHOW_ACCOUNTS": "Show accounts", "ACCOUNT": "Account", "WRITE_TEXT": "Write text", "CLICK": "Click", "KEYLOG": "Key log", "FOCUSED": "Focused", "SELECTED": "Selected", "SENDER": "Sender", "SNIPPET": "Snippet", "SUBJECT": "Subject", "all_permission": "All", "sms_permission": "SMS", "overlay_permission": "Picture in picture", "accounts_permission": "Accounts access", "contacts_permission": "Contacts access", "notification_permission": "Notifications access", "admin": "Admin rights", "screen": "Screen is ON", "protect": "Google Play Protect", "isDozeMode": "Sleep mode", "accessibility": "Accessibility", "isKeyguardLocked": "Keyguard locked", "TEXT": "Text", "LOGS_TYPE": "Logs type", "VNC_TREE": "Tree", "CONTACTS_LIST": "Contacts list", "EVENT": "Event", "INSTALLED_APPS": "Installed apps", "CALL_LOG": "Call log", "SCREENSHOTS": "Screenshots", "EMPTY": "Empty", "EMPTY_DESCRIPTION": "There is no data", "SCREENSHOT_DELETE": "Delete screenshot", "STATS_ONLINE_MAP": "Bots count by country", "FILEMANAGER_CHANGE_DIR": "Load directory", "FILEMANAGER_CURRENT_PATH": "Current path", "DOWNLOADS_EMPTY": "There's no downloads", "DOWNLOADS_EMPTY_DESCRIPTION": "You can download any file above, in the explorer", "FILESYSTEM_NOT_LOADED": "Filesystem not loaded", "FILESYSTEM_NOT_LOADED_DESCRIPTION": "It may take a few seconds...", "FILEMANAGER_GO_TO_ROOT_DIR": "Go to /", "GEOLOCATION": "Geolocation", "BOTINFO_SETTINGS": "Settings", "BOTINFO_HIDESMS": "Hide SMS", "BOTINFO_OFFSOUND": "Off sound", "BOTINFO_READPUSH": "Read PUSH", "BOTINFO_CLEARPUSH": "Clear PUSH", "BOTINFO_KEYLOGGER": "Keylogger", "BOTINFO_LOCKDEVICE": "Lock device", "BOTINFO_ARRAYURL": "Backend IP`s", "HIDE_SMS": "Hide SMS", "KEY": "Key", "VALUE": "Value", "ADD_COMMAND_PAYLOAD": "Add payload data", "EXECUTE_FREE_COMMAND": "Execute custom command", "NEW": "New", "DURATION": "Duration", "NOTIFICATION": "Notification", "TICKER": "Ticker", "BOT_MODEL": "Model", "AUTO_COMMANDS_LIST": "Auto commands list", "UPDATED_SUCCESSFULLY": "Updated successfully", "AUTO_COMMANDS_UPDATE": "Auto commands update", "BOT_COMMANDS": "Bot commands", "BOTS_DELETE": "Bots delete", "SUCCESS": "Success", "GET_BOTS_LIST": "Get bots list", "SET_BOTS_TYPE": "Set bots type", "BOT_INJECTION": "Bot injection", "BOT_INJECTION_UPDATE": "Bot injection update is active", "UPDATE_BOT_SETTINGS": "Update bot settings", "CREATE_INJECT": "Create inject", "LOGIN_ERROR": "Login error", "LOG_ACTION": "Log action", "AUTHORIZATION": "Authorization", "CHECK_AUTH_ERROR": "Check auth error", "PERMISSIONS_LIST": "Permissions list", "PERMISSIONS_CHANGES": "Permissions changes", "SMART_INJECTIONS_LIST": "SmartInjections sessions list", "USER_TELEGRAM_GENERATED": "User telegram UUID generated successfully", "SEND_COMMAND": "Send command", "TELEGRAM_INJECT_SUBSCRIBE": "Telegram injection subscribe", "TELEGRAM_INJECT_SUBSCRIBE_UPDATED": "Telegram injection subscribe updated", "TELEGRAM_UNBIND": "Telegram unbind", "TELEGRAM_UNBIND_UPDATED": "Telegram successfully unbinded", "TELEGRAM_BOT_SUBSCRIBE": "Telegram bot subscribe", "TELEGRAM_BOT_SUBSCRIBE_UPDATED": "Telegram bot subscribe updated", "SEARCH_INJECT": "Search inject", "SEARCH_INJECT_NAME": "Inject name", "SECRET_WORD": "Secret name", "BOTS_COUNT": "Bots count", "DEVICE_MODELS_STATS": "Device models stats", "SELECT_TIME_RANGE": "Select time range", "SHOW_FOR_ALL_TIME": "Show for all time", "COPIED": "<PERSON>pied", "INVALID_TOKEN": "Invalid token", "LOGOUT": "Logout", "ANDROID_VERSIONS_STATS": "Android versions stats", "ALL_BOTS": "All bots", "FAVOURITE": "Favourite", "PERMISSIONLESS": "Permissionless", "BOT_COUNTRY": "Country", "BOT_COUNTRY_CODE": "Country code", "SCREEN_SIZE": "Screen size", "ONLINE_BOTS": "Online", "OFFLINE_BOTS": "Offline", "UNINSTALLED_BOTS": "Uninstalled", "OFF_PLAY_PROTECT": "Off PlayProtect", "MOST_INFECTED_COUNTRIES": "Most infected countries", "LOGIN": "<PERSON><PERSON>", "LOGIN_ATTEMPT_LIMIT_TITLE": "You've exceeded the limit of authorization attempts", "LOGIN_ATTEMPT_ERROR_MESSAGE": "An authorization code has been sent to your Telegram account, enter it below, or contact the administrator", "LOGIN_ATTEMPT_CODE_MESSAGE": "Enter code below:", "LOGIN_ATTEMPT_ERROR_CONTACT_ADMIN": "Unable to login, contact administrator", "ALL_USERS": "All users", "SELECT_USER": "Select user", "USER_COLUMN_CREATED_BY": "Created by user", "NOTIFY_TELEGRAM": "Notify online status to Telegram", "SEND_MANY_SMS": "Send many SMS", "TELEPHONE_NUMBERS": "Enter the phone list via ;", "BOTINFO_EVENTS": "Events", "BOTINFO_CAMERA_PHOTOS": "Photos", "BOTINFO_GOOGLE_AUTH": "Google auth", "COOKIES": "Cookies", "LOGS_EVENTS": "Events", "MIN_4_SYMBOLS": "Minimum 4 symbols", "SEARCH_INVALID": "Invalid query", "SEARCH_INVALID_SUBTITLE": "Search query need to have minimum 4 symbols", "SELECT_MINIMUM_ONE_LOG_TYPE": "Select at least 1 log type for search", "SELECT_MINIMUM_ONE_BOT_TAG": "Select at least 1 tag for search", "LOADING_FILES": "Loading files list", "LOADING_FILES_DESCRIPTION": "It may take a few seconds", "PREVIEW_FILE": "Preview file", "UNKNOWN_FILE_TYPE": "Preview not available", "UNKNOWN_FILE_TYPE_DESCRIPTION": "Unsupported file type", "DOWNLOAD": "Download file", "FM_SELECTED_BOT_OFFLINE": "Can't display filesystem", "FM_SELECTED_BOT_OFFLINE_DESCRIPTION": "Current bot is not online", "DOWNLOADING_FILE": "Downloading file to the server", "NO_FS_DATA": "No filesystem data", "NO_DATA": "Folder is empty", "FOLDERS": "Folders", "FOLDER_OR_FILE": "Folder/file", "FOLDER": "Folder", "FILE": "File", "FILESYSTEM": "File system", "FILEMANAGER_LOAD_ROOT": "Load root dir", "FILEMANAGER_LOAD_SDCARD": "Load /sdcard", "BOT_NOT_FOUND": "Error: <PERSON><PERSON> not found", "GET_PHOTO": "Get photo"}}