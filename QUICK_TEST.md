# 🧪 Быстрое тестирование ERMAC

## 🚀 Локальное тестирование (для слабых ноутбуков)

### Шаг 1: Подготовка
```bash
# Убедитесь, что Docker запущен
docker --version
docker-compose --version

# Убедитесь, что Node.js установлен
node --version
npm --version
```

### Шаг 2: Запуск локального тестирования
```bash
# В корне проекта
./local_test.sh
```

### Шаг 3: Запуск фронтенда
```bash
# После завершения local_test.sh
cd frontend
npm start
```

### Шаг 4: Тестирование
- Откройте http://localhost:3000
- Логин: `root`
- Пароль: `changemeplease`

## 🌐 VPS установка (рекомендуется)

### Шаг 1: Подключение к VPS
```bash
ssh root@your-vps-ip
```

### Шаг 2: Установка
```bash
# Скачать скрипт
wget https://raw.githubusercontent.com/your-repo/ermac/main/install_panel_only.sh

# Запустить установку
chmod +x install_panel_only.sh
./install_panel_only.sh
```

### Шаг 3: Тестирование
- Откройте http://your-vps-ip
- Логин: `root`
- Пароль: `changemeplease`

## 🔧 Управление сервисами

```bash
# Статус сервисов
./manage_panel.sh status

# Остановить все
./manage_panel.sh stop

# Запустить все
./manage_panel.sh start

# Логи
./manage_panel.sh logs

# Информация о панели
./manage_panel.sh info
```

## 📱 Тестирование SMS функциональности

### 1. Проверка API
```bash
# Проверить доступность API
curl http://localhost:8089/api/v1/health
# или для VPS
curl http://your-vps-ip:8089/api/v1/health
```

### 2. Тестирование команд
1. Войдите в панель
2. Перейдите в раздел "Bots"
3. Нажмите кнопку команд (⚡)
4. Найдите команды:
   - "Get SMS" - получить SMS
   - "Set as Default SMS App" - установить как SMS приложение по умолчанию

### 3. Тестирование кнопки Show SMS
1. В карточке бота найдите кнопку "Show SMS" 📱
2. Нажмите на неё
3. Должно открыться модальное окно со списком SMS

## 🐛 Диагностика проблем

### Проверка портов
```bash
# Локально
netstat -tlnp | grep -E ':(3000|8089|3306)'

# На VPS
netstat -tlnp | grep -E ':(80|8089|3306)'
```

### Проверка логов
```bash
# Логи backend
./manage_panel.sh logs php

# Логи MySQL
./manage_panel.sh logs mysql

# Все логи
./manage_panel.sh logs
```

### Частые проблемы

1. **Порт 3000 занят (локально)**
   ```bash
   # Найти процесс
   lsof -i :3000
   # Убить процесс
   kill -9 <PID>
   ```

2. **MySQL не запускается**
   ```bash
   # Очистить данные
   docker volume rm $(docker volume ls -q | grep mysql)
   ./manage_panel.sh start
   ```

3. **Frontend не собирается**
   ```bash
   cd frontend
   rm -rf node_modules package-lock.json
   npm install
   npm start
   ```

## ✅ Чек-лист тестирования

### Базовая функциональность:
- [ ] Панель открывается
- [ ] Вход в систему работает
- [ ] API отвечает на запросы
- [ ] База данных подключена

### SMS функциональность:
- [ ] Команда "Get SMS" отправляется
- [ ] Команда "Set as Default SMS App" отправляется
- [ ] Кнопка "Show SMS" открывает модальное окно
- [ ] Модальное окно корректно отображается

### Производительность:
- [ ] Панель загружается быстро
- [ ] API отвечает в разумное время
- [ ] Нет критических ошибок в логах

## 📊 Мониторинг ресурсов

```bash
# Использование Docker
docker stats

# Использование системы
htop
# или
top

# Место на диске
df -h
```

## 🎯 Следующие шаги

После успешного тестирования:

1. **Настройте SSL** (для VPS)
2. **Смените пароли по умолчанию**
3. **Настройте мониторинг**
4. **Создайте бэкапы**
5. **Подключите реальные боты**

## 📞 Поддержка

Если что-то не работает:

1. Проверьте логи: `./manage_panel.sh logs`
2. Проверьте статус: `./manage_panel.sh status`
3. Перезапустите: `./manage_panel.sh restart`
4. Создайте issue с подробным описанием проблемы
