#!/bin/bash

# ERMAC Panel Installation Script (Without Gate Service & Builder)
# Simplified version for VPS deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

function echo_info {
    echo -e "${BLUE}[INFO]${NC} $1"
}

function echo_success {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

function echo_warning {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

function echo_error {
    echo -e "${RED}[ERROR]${NC} $1"
}

function generate_random_string {
    local length=$1
    local random_string=$(openssl rand -base64 $length | tr -dc 'a-zA-Z0-9' | cut -c1-$length)
    echo "$random_string"
}

echo_info "Starting ERMAC Panel installation..."
echo_info "This script will install only the panel (without gate service and builder)"

# Get server IP
echo_info "Detecting server IP..."
yourserverip=$(curl -s ipinfo.io/ip)
echo_success "Server IP detected: $yourserverip"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo_error "This script must be run as root"
   exit 1
fi

# Update system
echo_info "Updating system packages..."
apt-get update -y
apt-get upgrade -y

# Install required packages
echo_info "Installing required packages..."
apt-get install -y \
    apt-transport-https \
    ca-certificates \
    curl \
    gnupg \
    lsb-release \
    build-essential \
    wget \
    ufw \
    dos2unix \
    openssl

# Install Docker
echo_info "Installing Docker..."
if ! command -v docker &> /dev/null; then
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
    apt-get update -y
    apt-get install -y docker-ce docker-ce-cli containerd.io
else
    echo_success "Docker already installed"
fi

# Install Docker Compose
echo_info "Installing Docker Compose..."
if ! command -v docker-compose &> /dev/null; then
    curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
else
    echo_success "Docker Compose already installed"
fi

# Start Docker service
systemctl start docker
systemctl enable docker

# Generate environment variables
echo_info "Generating environment configuration..."

MYSQL_PASSWORD=$(generate_random_string 23)
MYSQL_ROOT_PASSWORD=$(generate_random_string 23)
MYSQL_USER="ermac_$(generate_random_string 15)"
MYSQL_DATABASE="ermac_$(generate_random_string 15)"
KEYBACKEND=$(generate_random_string 32)

BACKEND_PORT="8089"
BACKEND_URL="http://$yourserverip:8089/"
FRONTEND_URL="http://$yourserverip"
FRONTEND_PORT="80"
MYSQL_PORT="3306"
PHP_SOCKETS_PORT="8000"
TIMEZONE="UTC"

# Create .env file
echo_info "Creating environment file..."
cat > .env << EOF
BACKEND_INTERFACE=0.0.0.0
BACKEND_PORT=${BACKEND_PORT}
BACKEND_URL=${BACKEND_URL}
BACKEND_DEBUG=false

FRONTEND_URL=${FRONTEND_URL}
FRONTEND_INTERFACE=0.0.0.0
FRONTEND_PORT=${FRONTEND_PORT}

MYSQL_PORT=${MYSQL_PORT}
MYSQL_DATABASE=${MYSQL_DATABASE}
MYSQL_USER=${MYSQL_USER}
MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
MYSQL_PASSWORD=${MYSQL_PASSWORD}
MYSQL_HOST=mysql

PHP_SOCKETS_PORT=${PHP_SOCKETS_PORT}

TIMEZONE=${TIMEZONE}
EOF

echo_success "Environment file created"

# Create simplified docker-compose.yml (without golang gate service)
echo_info "Creating Docker Compose configuration..."

cat > docker-compose-panel.yml << 'EOF'
version: '3.5'

services:
  nginx:
    container_name: ermac_panel_nginx
    image: nginx:alpine
    restart: unless-stopped
    depends_on:
      - php
      - node
    volumes:
      - ./backend/public:/var/www/public:ro
      - ./frontend/build:/usr/share/nginx/html:ro
      - ./docker/nginx/conf.d/:/etc/nginx/conf.d:ro
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/log/:/var/log/nginx/
    ports:
      - "80:80"
      - "8089:8080"
    networks:
      - frontend
      - backend

  php:
    container_name: ermac_panel_php
    build:
      context: .
      target: php
    restart: unless-stopped
    depends_on:
      - mysql
    volumes:
      - ./backend/:/var/www:rw
      - ./backend/storage/:/var/www/storage/:rw
    working_dir: /var/www
    networks:
      - backend
    ports:
      - "${PHP_SOCKETS_PORT}:8000"
    environment:
      DB_HOST: mysql
      DB_PORT: 3306
      DB_DATABASE: ${MYSQL_DATABASE}
      DB_USERNAME: ${MYSQL_USER}
      DB_PASSWORD: ${MYSQL_PASSWORD}
      DB_TIMEZONE: ${TIMEZONE}
      APP_TIMEZONE: ${TIMEZONE}
      APP_URL: ${BACKEND_URL}
      APP_DEBUG: false

  node:
    container_name: ermac_panel_node
    image: node:16-alpine
    working_dir: /var/www
    tty: true
    volumes:
      - ./frontend/:/var/www:rw
    networks:
      - frontend

  mysql:
    image: mysql:8.0
    container_name: ermac_panel_mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
      MYSQL_ROOT_HOST: '%'
    volumes:
      - ./docker/mysql/my.cnf:/etc/mysql/my.cnf
      - ./mysqldata:/var/lib/mysql:rw
    networks:
      - backend
    ports:
      - "3306:3306"

  phpmyadmin:
    container_name: ermac_panel_phpmyadmin
    image: phpmyadmin/phpmyadmin:latest
    restart: unless-stopped
    depends_on:
      - mysql
    environment:
      PMA_HOST: mysql
      PMA_ABSOLUTE_URI: /phpmyadmin/
      UPLOAD_LIMIT: 512M
    networks:
      - backend
      - frontend

networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
EOF

echo_success "Docker Compose configuration created"

# Setup backend environment
echo_info "Setting up backend environment..."
if [[ ! -f backend/.env ]]; then
    cp backend/.env.example backend/.env
fi

# Setup frontend environment
echo_info "Setting up frontend environment..."
if [[ ! -f frontend/.env ]]; then
    cp frontend/.env.example frontend/.env
fi

if [[ ! -f frontend/.env.production ]]; then
    cp frontend/.env.production.example frontend/.env.production
fi

# Configure frontend URLs
sed -i "s|REACT_APP_BACKEND_URL=.*|REACT_APP_BACKEND_URL=http://$yourserverip:8089/|g" frontend/.env
sed -i "s|REACT_APP_BACKEND_URL=.*|REACT_APP_BACKEND_URL=http://$yourserverip:8089/|g" frontend/.env.production
sed -i "s|REACT_APP_TIMEZONE=.*|REACT_APP_TIMEZONE=${TIMEZONE}|g" frontend/.env
sed -i "s|REACT_APP_TIMEZONE=.*|REACT_APP_TIMEZONE=${TIMEZONE}|g" frontend/.env.production

# Note: No Socket.IO URL since we're not using gate service
echo_warning "Socket.IO (real-time features) disabled - no gate service"

# Configure firewall
echo_info "Configuring firewall..."
ufw --force enable
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 8089/tcp  # Backend API
ufw allow 3306/tcp  # MySQL (for external access if needed)

# Build and start containers
echo_info "Building Docker containers..."
docker-compose -f docker-compose-panel.yml build

echo_info "Starting services..."
docker-compose -f docker-compose-panel.yml up -d

# Wait for services to start
echo_info "Waiting for services to start..."
sleep 30

# Set permissions
echo_info "Setting file permissions..."
docker-compose -f docker-compose-panel.yml exec -T php chown -R www-data:www-data .

# Install backend dependencies
echo_info "Installing backend dependencies..."
docker-compose -f docker-compose-panel.yml exec -T php composer install --optimize-autoloader --no-dev

# Install frontend dependencies and build
echo_info "Installing frontend dependencies..."
docker-compose -f docker-compose-panel.yml exec -T node npm install

echo_info "Building frontend..."
docker-compose -f docker-compose-panel.yml exec -T node npm run build

# Setup database
echo_info "Setting up database..."
docker-compose -f docker-compose-panel.yml exec -T php php artisan migrate --force
docker-compose -f docker-compose-panel.yml exec -T php php artisan db:seed --force

# Generate application keys
echo_info "Generating application keys..."
docker-compose -f docker-compose-panel.yml exec -T php php artisan jwt:secret --force
docker-compose -f docker-compose-panel.yml exec -T php php artisan key:generate --force

# Cache configuration
echo_info "Caching configuration..."
docker-compose -f docker-compose-panel.yml exec -T php php artisan config:cache
docker-compose -f docker-compose-panel.yml exec -T php php artisan route:cache

# Final permissions
docker-compose -f docker-compose-panel.yml exec -T php chown -R www-data:www-data .

# Create credentials file
echo_info "Saving installation credentials..."
cat > /root/ermac_panel_credentials.txt << EOF
=== ERMAC PANEL INSTALLATION COMPLETE ===

Panel URL: http://$yourserverip
Backend API: http://$yourserverip:8089
PhpMyAdmin: http://$yourserverip/phpmyadmin

Database Credentials:
- Database: $MYSQL_DATABASE
- Username: $MYSQL_USER
- Password: $MYSQL_PASSWORD
- Root Password: $MYSQL_ROOT_PASSWORD

Default Panel Login:
- Username: root
- Password: changemeplease

Backend Key: $KEYBACKEND

Services Status:
- Panel: ✅ Running (Port 80)
- Backend API: ✅ Running (Port 8089)
- MySQL: ✅ Running (Port 3306)
- PhpMyAdmin: ✅ Running

Note: Real-time features (Socket.IO) are disabled in this installation.
To enable them, you need to install the gate service separately.

Installation Date: $(date)
Server IP: $yourserverip
EOF

# Display final information
echo ""
echo_success "🎉 ERMAC Panel installation completed successfully!"
echo ""
echo_info "📋 Installation Summary:"
echo "   Panel URL: http://$yourserverip"
echo "   Backend API: http://$yourserverip:8089"
echo "   PhpMyAdmin: http://$yourserverip/phpmyadmin"
echo ""
echo_info "🔑 Default Login Credentials:"
echo "   Username: root"
echo "   Password: changemeplease"
echo ""
echo_info "💾 Database Information:"
echo "   Database: $MYSQL_DATABASE"
echo "   Username: $MYSQL_USER"
echo "   Password: $MYSQL_PASSWORD"
echo ""
echo_warning "⚠️  Important Notes:"
echo "   1. Change default password after first login"
echo "   2. Real-time features are disabled (no gate service)"
echo "   3. Builder service is not included in this installation"
echo "   4. All credentials saved to: /root/ermac_panel_credentials.txt"
echo ""
echo_info "🚀 Next Steps:"
echo "   1. Open http://$yourserverip in your browser"
echo "   2. Login with default credentials"
echo "   3. Change password in settings"
echo "   4. Configure your bots to connect to: http://$yourserverip:8089"
echo ""
echo_success "Installation completed! 🎊"
