#!/bin/bash

# ERMAC Local Builder Management Script
# Manage local builder container for APK testing

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

function echo_info {
    echo -e "${BLUE}[BUILDER]${NC} $1"
}

function echo_success {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

function echo_warning {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

function echo_error {
    echo -e "${RED}[ERROR]${NC} $1"
}

CONTAINER_NAME="ermac_local_builder"

function show_status {
    echo_info "Local Builder Status:"
    
    if docker ps | grep -q $CONTAINER_NAME; then
        echo_success "✅ Builder is running"
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep $CONTAINER_NAME
        echo ""
        
        echo_info "Resource Usage:"
        docker stats $CONTAINER_NAME --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"
        echo ""
        
        echo_info "Connectivity Test:"
        if curl -s http://localhost:8080 > /dev/null; then
            echo_success "✅ Builder web interface accessible"
        else
            echo_error "❌ Builder web interface not accessible"
        fi
    else
        echo_error "❌ Builder is not running"
        if docker ps -a | grep -q $CONTAINER_NAME; then
            echo_info "Container exists but is stopped"
        else
            echo_info "Container does not exist. Run ./local_builder_test.sh first"
        fi
    fi
}

function show_logs {
    local lines=${1:-50}
    echo_info "Showing last $lines lines of builder logs..."
    docker logs --tail=$lines $CONTAINER_NAME
}

function follow_logs {
    echo_info "Following builder logs (Ctrl+C to stop)..."
    docker logs -f $CONTAINER_NAME
}

function start_builder {
    if docker ps | grep -q $CONTAINER_NAME; then
        echo_warning "Builder is already running"
        return 0
    fi
    
    if docker ps -a | grep -q $CONTAINER_NAME; then
        echo_info "Starting existing builder container..."
        docker start $CONTAINER_NAME
    else
        echo_error "Builder container does not exist. Run ./local_builder_test.sh first"
        return 1
    fi
    
    echo_info "Waiting for builder to start..."
    sleep 10
    
    if curl -s http://localhost:8080 > /dev/null; then
        echo_success "Builder started successfully!"
    else
        echo_warning "Builder may still be starting. Check logs if needed."
    fi
}

function stop_builder {
    echo_info "Stopping builder..."
    docker stop $CONTAINER_NAME
    echo_success "Builder stopped"
}

function restart_builder {
    echo_info "Restarting builder..."
    docker restart $CONTAINER_NAME
    
    echo_info "Waiting for builder to restart..."
    sleep 15
    
    if curl -s http://localhost:8080 > /dev/null; then
        echo_success "Builder restarted successfully!"
    else
        echo_warning "Builder may still be starting. Check logs if needed."
    fi
}

function remove_builder {
    echo_warning "This will completely remove the builder container and image."
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo_info "Stopping and removing container..."
        docker stop $CONTAINER_NAME 2>/dev/null || true
        docker rm $CONTAINER_NAME 2>/dev/null || true
        
        echo_info "Removing builder image..."
        docker rmi ermac_local_builder 2>/dev/null || true
        
        echo_success "Builder removed completely"
    else
        echo_info "Removal cancelled"
    fi
}

function access_builder {
    if ! docker ps | grep -q $CONTAINER_NAME; then
        echo_error "Builder is not running"
        return 1
    fi
    
    echo_info "Accessing builder container shell..."
    docker exec -it $CONTAINER_NAME bash
}

function show_builds {
    echo_info "Recent APK builds:"
    if docker ps | grep -q $CONTAINER_NAME; then
        docker exec $CONTAINER_NAME find /var/www/build -name "*.apk" -printf "%T@ %Tc %p\n" 2>/dev/null | sort -n | tail -10 || echo "No APK files found"
    else
        echo_error "Builder is not running"
    fi
}

function clean_builds {
    if ! docker ps | grep -q $CONTAINER_NAME; then
        echo_error "Builder is not running"
        return 1
    fi
    
    echo_info "Cleaning old builds and cache..."
    docker exec $CONTAINER_NAME find /var/www/build -name "*.apk" -mtime +1 -delete 2>/dev/null || true
    docker exec $CONTAINER_NAME find /var/www/build -name "*.tmp" -delete 2>/dev/null || true
    docker exec $CONTAINER_NAME rm -rf /var/www/build/tmpfile/* 2>/dev/null || true
    
    echo_success "Build cache cleaned"
}

function test_builder {
    echo_info "Testing builder functionality..."
    
    # Test container status
    if ! docker ps | grep -q $CONTAINER_NAME; then
        echo_error "❌ Builder container is not running"
        return 1
    fi
    
    echo_success "✅ Container is running"
    
    # Test web interface
    if curl -s http://localhost:8080 > /dev/null; then
        echo_success "✅ Web interface accessible"
    else
        echo_error "❌ Web interface not accessible"
        return 1
    fi
    
    # Test build tools
    if docker exec $CONTAINER_NAME which java > /dev/null 2>&1; then
        echo_success "✅ Java available"
    else
        echo_error "❌ Java not found"
    fi
    
    if docker exec $CONTAINER_NAME which apktool > /dev/null 2>&1; then
        echo_success "✅ Apktool available"
    else
        echo_error "❌ Apktool not found"
    fi
    
    if docker exec $CONTAINER_NAME test -d /usr/lib/android-sdk; then
        echo_success "✅ Android SDK available"
    else
        echo_error "❌ Android SDK not found"
    fi
    
    echo_info "Builder test completed!"
}

function show_info {
    echo_info "Local Builder Information:"
    echo ""
    
    echo "🏗️ Builder Status:"
    if docker ps | grep -q $CONTAINER_NAME; then
        echo "   Status: ✅ Running"
        echo "   URL: http://localhost:8080"
        echo "   Container: $CONTAINER_NAME"
    else
        echo "   Status: ❌ Not running"
    fi
    echo ""
    
    if docker ps | grep -q $CONTAINER_NAME; then
        echo "💾 Resource Usage:"
        docker stats $CONTAINER_NAME --no-stream --format "   CPU: {{.CPUPerc}} | Memory: {{.MemUsage}} ({{.MemPerc}})"
        echo ""
        
        echo "📦 Recent Builds:"
        docker exec $CONTAINER_NAME find /var/www/build -name "*.apk" -printf "%Tc %f\n" 2>/dev/null | tail -5 || echo "   No builds found"
        echo ""
    fi
    
    echo "🔧 Available Commands:"
    echo "   ./manage_local_builder.sh status    - Show status"
    echo "   ./manage_local_builder.sh logs      - Show logs"
    echo "   ./manage_local_builder.sh restart   - Restart builder"
    echo "   ./manage_local_builder.sh test      - Test functionality"
    echo "   ./manage_local_builder.sh builds    - Show recent builds"
    echo "   ./manage_local_builder.sh clean     - Clean old builds"
}

function show_help {
    echo "ERMAC Local Builder Management"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  status      - Show builder status and resource usage"
    echo "  logs        - Show recent builder logs"
    echo "  logs-follow - Follow builder logs in real-time"
    echo "  start       - Start builder container"
    echo "  stop        - Stop builder container"
    echo "  restart     - Restart builder container"
    echo "  remove      - Remove builder container and image"
    echo "  shell       - Access builder container shell"
    echo "  builds      - Show recent APK builds"
    echo "  clean       - Clean old builds and cache"
    echo "  test        - Test builder functionality"
    echo "  info        - Show comprehensive builder information"
    echo "  help        - Show this help"
    echo ""
    echo "Examples:"
    echo "  $0 status"
    echo "  $0 logs-follow"
    echo "  $0 test"
}

# Main script logic
case "$1" in
    "status")
        show_status
        ;;
    "logs")
        show_logs ${2:-50}
        ;;
    "logs-follow")
        follow_logs
        ;;
    "start")
        start_builder
        ;;
    "stop")
        stop_builder
        ;;
    "restart")
        restart_builder
        ;;
    "remove")
        remove_builder
        ;;
    "shell")
        access_builder
        ;;
    "builds")
        show_builds
        ;;
    "clean")
        clean_builds
        ;;
    "test")
        test_builder
        ;;
    "info")
        show_info
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    "")
        echo_warning "No command specified. Use 'help' for available commands."
        show_info
        ;;
    *)
        echo_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
