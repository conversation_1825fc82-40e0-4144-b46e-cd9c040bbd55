# 🚀 ERMAC Installation Guide

Этот гид содержит инструкции по установке ERMAC панели в различных конфигурациях.

## 📋 Доступные скрипты

### 1. `install_panel_only.sh` - Полная установка на VPS
**Для продакшена на VPS без gate сервиса и builder**

```bash
# Скачать и запустить
wget https://your-repo.com/install_panel_only.sh
chmod +x install_panel_only.sh
sudo ./install_panel_only.sh
```

**Что устанавливается:**
- ✅ Nginx (веб-сервер)
- ✅ PHP Backend (API)
- ✅ MySQL (база данных)
- ✅ React Frontend (панель управления)
- ✅ PhpMyAdmin (управление БД)
- ❌ Gate Service (Socket.IO) - отключен
- ❌ Builder - не включен

### 2. `local_test.sh` - Локальное тестирование
**Для тестирования на слабых ноутбуках**

```bash
# В корне проекта
chmod +x local_test.sh
./local_test.sh
```

**Особенности:**
- 🔧 Только MySQL + PHP Backend в Docker
- 🚀 Frontend запускается через `npm start` (быстрее)
- 💾 Минимальное потребление ресурсов
- 🧪 Режим разработки

## 🔧 Системные требования

### Для VPS (install_panel_only.sh)
- **OS:** Ubuntu 20.04+ / Debian 11+
- **RAM:** 2GB минимум, 4GB рекомендуется
- **CPU:** 2 ядра минимум
- **Диск:** 20GB свободного места
- **Сеть:** Статический IP

### Для локального тестирования (local_test.sh)
- **OS:** Linux/macOS/Windows (с WSL)
- **RAM:** 4GB минимум
- **Docker:** Установлен и запущен
- **Node.js:** 16+ версия
- **Диск:** 5GB свободного места

## 🚀 Быстрый старт

### VPS установка (рекомендуется)

```bash
# 1. Подключиться к VPS
ssh root@your-vps-ip

# 2. Скачать скрипт
wget https://raw.githubusercontent.com/your-repo/ermac/main/install_panel_only.sh

# 3. Запустить установку
chmod +x install_panel_only.sh
./install_panel_only.sh

# 4. Дождаться завершения (5-10 минут)
# 5. Открыть http://your-vps-ip в браузере
```

### Локальное тестирование

```bash
# 1. Клонировать репозиторий
git clone https://github.com/your-repo/ermac.git
cd ermac

# 2. Запустить локальный тест
chmod +x local_test.sh
./local_test.sh

# 3. Запустить фронтенд
cd frontend
npm start

# 4. Открыть http://localhost:3000
```

## 🔑 Доступы по умолчанию

### Панель управления
- **URL:** http://your-ip (VPS) или http://localhost:3000 (локально)
- **Логин:** root
- **Пароль:** changemeplease

### База данных
- **Host:** localhost:3306
- **Database:** ermac_* (генерируется автоматически)
- **Username:** ermac_* (генерируется автоматически)
- **Password:** (генерируется автоматически)

### PhpMyAdmin (только VPS)
- **URL:** http://your-ip/phpmyadmin

## 🔧 Управление сервисами

### VPS (Docker Compose)
```bash
# Статус сервисов
docker-compose -f docker-compose-panel.yml ps

# Остановить все
docker-compose -f docker-compose-panel.yml down

# Запустить все
docker-compose -f docker-compose-panel.yml up -d

# Перезапустить конкретный сервис
docker-compose -f docker-compose-panel.yml restart nginx

# Логи
docker-compose -f docker-compose-panel.yml logs -f php
```

### Локальное тестирование
```bash
# Остановить backend
docker-compose -f docker-compose.local.yml down

# Запустить backend
docker-compose -f docker-compose.local.yml up -d

# Запустить frontend
cd frontend && npm start
```

## 🛠️ Настройка ботов

### Конфигурация бота
После установки панели, настройте ботов для подключения:

```kotlin
// В коде Android бота
const val BACKEND_URL = "http://your-vps-ip:8089/"
```

### Тестирование подключения
```bash
# Проверить доступность API
curl http://your-vps-ip:8089/api/v1/health

# Должен вернуть: {"status": "ok"}
```

## 🔍 Диагностика проблем

### Проверка портов
```bash
# Проверить открытые порты
netstat -tlnp | grep -E ':(80|8089|3306)'

# Проверить firewall
ufw status
```

### Логи сервисов
```bash
# Логи Nginx
docker logs ermac_panel_nginx

# Логи PHP Backend
docker logs ermac_panel_php

# Логи MySQL
docker logs ermac_panel_mysql
```

### Частые проблемы

1. **Порт 80 занят**
   ```bash
   # Найти процесс
   sudo lsof -i :80
   # Остановить Apache/Nginx
   sudo systemctl stop apache2 nginx
   ```

2. **MySQL не запускается**
   ```bash
   # Очистить данные MySQL
   sudo rm -rf mysqldata/
   docker-compose -f docker-compose-panel.yml up -d mysql
   ```

3. **Frontend не собирается**
   ```bash
   # Очистить кеш npm
   cd frontend
   rm -rf node_modules package-lock.json
   npm install
   ```

## 📞 Поддержка

При возникновении проблем:

1. Проверьте логи сервисов
2. Убедитесь, что все порты открыты
3. Проверьте файл `/root/ermac_panel_credentials.txt` с данными установки
4. Создайте issue в репозитории с подробным описанием проблемы

## 🔒 Безопасность

### Обязательные действия после установки:

1. **Смените пароль по умолчанию**
2. **Настройте SSL сертификат**
3. **Ограничьте доступ к MySQL**
4. **Настройте регулярные бэкапы**

### Рекомендации:
- Используйте сильные пароли
- Регулярно обновляйте систему
- Мониторьте логи на подозрительную активность
- Настройте fail2ban для защиты от брутфорса
