#!/bin/bash

# ERMAC Local Builder Test Script
# Quick builder setup for local testing and APK creation

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

function echo_info {
    echo -e "${BLUE}[BUILDER]${NC} $1"
}

function echo_success {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

function echo_warning {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

function echo_error {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo_info "Starting ERMAC Local Builder Test..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if we're in the right directory
if [[ ! -d "builder" ]]; then
    echo_error "Builder directory not found. Please run this script from the project root."
    exit 1
fi

# Check system resources
TOTAL_RAM=$(free -m | awk 'NR==2{printf "%.0f", $2}')
AVAILABLE_DISK=$(df . | awk 'NR==2 {print $4}')

if [ $TOTAL_RAM -lt 4096 ]; then
    echo_warning "Warning: Less than 4GB RAM detected ($TOTAL_RAM MB). Builder may be slow."
    echo_warning "Consider closing other applications to free up memory."
fi

if [ $AVAILABLE_DISK -lt 5242880 ]; then # 5GB in KB
    echo_error "Error: Less than 5GB disk space available. Builder requires space for Android SDK."
    exit 1
fi

echo_success "System check passed"

# Stop any existing builder container
echo_info "Stopping any existing builder containers..."
docker stop ermac_local_builder 2>/dev/null || true
docker rm ermac_local_builder 2>/dev/null || true

# Check if builder image already exists
if docker images | grep -q "ermac_local_builder"; then
    echo_info "Builder image already exists, skipping build..."
else
    # Build the builder image
    echo_info "Building builder Docker image (this may take 15-30 minutes)..."
    echo_warning "This is a one-time process. Subsequent runs will be much faster."
fi

cd builder

# Create a simplified Dockerfile for local testing
cat > Dockerfile.local << 'EOF'
FROM ubuntu:jammy

ENV OS_LOCALE="en_US.UTF-8"
RUN apt-get update && apt-get install -y locales && locale-gen ${OS_LOCALE}

ENV LANG=${OS_LOCALE} \
    LANGUAGE=${OS_LOCALE} \
    LC_ALL=${OS_LOCALE} \
    DEBIAN_FRONTEND=noninteractive

ENV APACHE_CONF_DIR=/etc/apache2 \
    PHP_CONF_DIR=/etc/php/8.1 \
    PHP_DATA_DIR=/var/lib/php

# Export the Android SDK path
ENV ANDROID_HOME=/usr/lib/android-sdk
ENV ANDROID_SDK_ROOT=/usr/lib/android-sdk
ENV APKTOOL_VERSION="2.6.0"
ENV PATH="${PATH}:${ANDROID_HOME}/tools/bin:/usr/local/bin"

# Install basic packages
RUN apt-get update && apt-get install -y \
    curl apache2 libapache2-mod-php8.1 php8.1-cli php8.1-dev \
    php8.1-common php8.1-mysql php8.1-zip php8.1-gd php8.1-mbstring \
    php8.1-curl php8.1-xml php8.1-bcmath build-essential \
    default-jdk default-jre zipalign unzip android-sdk wget \
    imagemagick python3.10 python3-pip openjdk-11-jdk-headless \
    && rm -rf /var/lib/apt/lists/*

# Apache settings
RUN cp /dev/null ${APACHE_CONF_DIR}/conf-available/other-vhosts-access-log.conf \
    && rm ${APACHE_CONF_DIR}/sites-enabled/000-default.conf ${APACHE_CONF_DIR}/sites-available/000-default.conf \
    && a2enmod rewrite php8.1 \
    && ln -sf /dev/stdout /var/log/apache2/access.log \
    && ln -sf /dev/stderr /var/log/apache2/error.log

# Install Apktool
RUN wget -q "https://raw.githubusercontent.com/iBotPeaches/Apktool/master/scripts/linux/apktool" \
    -O /usr/local/bin/apktool && chmod a+x /usr/local/bin/apktool && \
    wget -q "https://bitbucket.org/iBotPeaches/apktool/downloads/apktool_${APKTOOL_VERSION}.jar" \
    -O /usr/local/bin/apktool.jar && chmod a+x /usr/local/bin/apktool.jar

# Setup Android SDK
RUN wget -q https://dl.google.com/android/repository/commandlinetools-linux-8512546_latest.zip \
    && unzip -q commandlinetools-linux-8512546_latest.zip -d $ANDROID_HOME/cmdline-tools \
    && mv $ANDROID_HOME/cmdline-tools/cmdline-tools $ANDROID_HOME/cmdline-tools/tools \
    && rm commandlinetools-linux-8512546_latest.zip

ENV PATH=":${ANDROID_HOME}/tools/bin:${ANDROID_HOME}/cmdline-tools/tools/bin:${ANDROID_HOME}/cmdline-tools/tools:${PATH}"

# Accept licenses and install build tools
RUN yes | sdkmanager --licenses && \
    sdkmanager --install "build-tools;28.0.3" \
                         "build-tools;30.0.3" \
                         "build-tools;33.0.0" \
                         "platform-tools" \
                         "platforms;android-28" \
                         "platforms;android-30"

# Create directories
RUN mkdir -p /var/www/app /var/www/build/tmpfile /var/www/build/buildFile \
             /var/www/source /var/www/Obfuscapk

# Copy configurations
COPY ./configs/apache2.conf ${APACHE_CONF_DIR}/apache2.conf
COPY ./configs/app.conf ${APACHE_CONF_DIR}/sites-enabled/app.conf
COPY ./configs/php.ini ${PHP_CONF_DIR}/apache2/conf.d/custom.ini

# Copy application files
COPY ./app /var/www/app/
COPY ./source /var/www/source/
COPY ./build /var/www/build/
COPY ./Obfuscapk /var/www/Obfuscapk/

# Set permissions
RUN chown -R www-data:www-data /var/www/ && \
    chmod -R 777 /var/www/build/

# Install Python dependencies for Obfuscapk
RUN pip3 install --upgrade setuptools && \
    pip3 install -r /var/www/Obfuscapk/src/requirements.txt || true

EXPOSE 80

# Copy entrypoint
COPY entrypoint.sh /sbin/entrypoint.sh
RUN chmod 755 /sbin/entrypoint.sh

CMD ["/sbin/entrypoint.sh"]
EOF

echo_info "Building optimized local builder image..."
docker build -f Dockerfile.local -t ermac_local_builder .

echo_success "Builder image built successfully!"

# Start the builder container
echo_info "Starting builder container..."
docker run -d \
    --name ermac_local_builder \
    -p 8080:80 \
    -v "$(pwd)/build:/var/www/build" \
    -v "$(pwd)/source:/var/www/source" \
    --memory=4g \
    --cpus=2 \
    ermac_local_builder

echo_info "Waiting for builder to start..."
sleep 15

# Check if builder is accessible
for i in {1..12}; do
    if curl -s http://localhost:8080 > /dev/null 2>&1; then
        echo_success "✅ Builder is ready!"
        break
    else
        echo_info "Waiting for builder... ($i/12)"
        sleep 10
    fi
done

# Final check
if curl -s http://localhost:8080 > /dev/null 2>&1; then
    echo_success "🎉 Builder is running successfully!"
    echo ""
    echo_info "📋 Builder Information:"
    echo "   Builder URL: http://localhost:8080"
    echo "   Container: ermac_local_builder"
    echo ""
    echo_info "🔧 Management Commands:"
    echo "   docker logs ermac_local_builder          # View logs"
    echo "   docker restart ermac_local_builder       # Restart builder"
    echo "   docker stop ermac_local_builder          # Stop builder"
    echo "   docker exec -it ermac_local_builder bash # Access container"
    echo ""
    echo_info "🏗️ APK Building:"
    echo "   1. Open http://localhost:8080 in your browser"
    echo "   2. Upload your bot source code"
    echo "   3. Configure build settings"
    echo "   4. Start the build process"
    echo "   5. Download the generated APK"
    echo ""
    echo_warning "⚠️  Notes:"
    echo "   - First APK build may take 10-15 minutes"
    echo "   - Subsequent builds will be faster"
    echo "   - Monitor progress with: docker logs -f ermac_local_builder"
    echo ""
    echo_success "Ready to build APKs! 🚀"
else
    echo_error "❌ Builder failed to start properly"
    echo_info "Check logs with: docker logs ermac_local_builder"
    exit 1
fi

cd ..
