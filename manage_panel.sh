#!/bin/bash

# ERMAC Panel Management Script
# Quick management tool for ERMAC panel services

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

function echo_info {
    echo -e "${BLUE}[INFO]${NC} $1"
}

function echo_success {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

function echo_warning {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

function echo_error {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Detect compose file
COMPOSE_FILE=""
if [[ -f "docker-compose-panel.yml" ]]; then
    COMPOSE_FILE="docker-compose-panel.yml"
elif [[ -f "docker-compose.local.yml" ]]; then
    COMPOSE_FILE="docker-compose.local.yml"
elif [[ -f "docker-compose.yml" ]]; then
    COMPOSE_FILE="docker-compose.yml"
else
    echo_error "No docker-compose file found!"
    exit 1
fi

echo_info "Using compose file: $COMPOSE_FILE"

function show_status {
    echo_info "Service Status:"
    docker-compose -f $COMPOSE_FILE ps
}

function start_services {
    echo_info "Starting all services..."
    docker-compose -f $COMPOSE_FILE up -d
    echo_success "Services started!"
}

function stop_services {
    echo_info "Stopping all services..."
    docker-compose -f $COMPOSE_FILE down
    echo_success "Services stopped!"
}

function restart_services {
    echo_info "Restarting all services..."
    docker-compose -f $COMPOSE_FILE restart
    echo_success "Services restarted!"
}

function show_logs {
    local service=$1
    if [[ -z "$service" ]]; then
        echo_info "Showing logs for all services..."
        docker-compose -f $COMPOSE_FILE logs -f
    else
        echo_info "Showing logs for $service..."
        docker-compose -f $COMPOSE_FILE logs -f $service
    fi
}

function update_panel {
    echo_info "Updating panel..."
    
    # Pull latest changes
    git pull
    
    # Rebuild containers
    docker-compose -f $COMPOSE_FILE build --no-cache
    
    # Restart services
    docker-compose -f $COMPOSE_FILE up -d
    
    # Update backend
    docker-compose -f $COMPOSE_FILE exec -T php composer install --optimize-autoloader --no-dev
    docker-compose -f $COMPOSE_FILE exec -T php php artisan migrate --force
    docker-compose -f $COMPOSE_FILE exec -T php php artisan config:cache
    docker-compose -f $COMPOSE_FILE exec -T php php artisan route:cache
    
    # Update frontend (if node service exists)
    if docker-compose -f $COMPOSE_FILE ps | grep -q node; then
        docker-compose -f $COMPOSE_FILE exec -T node npm install
        docker-compose -f $COMPOSE_FILE exec -T node npm run build
    fi
    
    echo_success "Panel updated!"
}

function backup_database {
    local backup_file="ermac_backup_$(date +%Y%m%d_%H%M%S).sql"
    echo_info "Creating database backup: $backup_file"
    
    # Get database credentials from .env or compose file
    DB_NAME=$(grep MYSQL_DATABASE .env 2>/dev/null | cut -d'=' -f2 || echo "ermac_local")
    DB_USER=$(grep MYSQL_USER .env 2>/dev/null | cut -d'=' -f2 || echo "ermac_user")
    DB_PASS=$(grep MYSQL_PASSWORD .env 2>/dev/null | cut -d'=' -f2 || echo "user123")
    
    docker-compose -f $COMPOSE_FILE exec -T mysql mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $backup_file
    
    if [[ $? -eq 0 ]]; then
        echo_success "Database backup created: $backup_file"
    else
        echo_error "Failed to create database backup"
    fi
}

function show_info {
    echo_info "ERMAC Panel Information:"
    echo ""
    
    # Get server IP
    SERVER_IP=$(curl -s ipinfo.io/ip 2>/dev/null || echo "localhost")
    
    echo "🌐 Panel URLs:"
    if [[ "$COMPOSE_FILE" == "docker-compose.local.yml" ]]; then
        echo "   Frontend: http://localhost:3000"
        echo "   Backend:  http://localhost:8089"
    else
        echo "   Frontend: http://$SERVER_IP"
        echo "   Backend:  http://$SERVER_IP:8089"
        echo "   PhpMyAdmin: http://$SERVER_IP/phpmyadmin"
    fi
    
    echo ""
    echo "🔑 Default Credentials:"
    echo "   Username: root"
    echo "   Password: changemeplease"
    
    echo ""
    echo "📊 Service Status:"
    docker-compose -f $COMPOSE_FILE ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"
    
    echo ""
    echo "💾 Database Info:"
    if [[ -f ".env" ]]; then
        DB_NAME=$(grep MYSQL_DATABASE .env | cut -d'=' -f2)
        DB_USER=$(grep MYSQL_USER .env | cut -d'=' -f2)
        echo "   Database: $DB_NAME"
        echo "   Username: $DB_USER"
    fi
    
    echo ""
    echo "📁 Important Files:"
    echo "   Credentials: /root/ermac_panel_credentials.txt"
    echo "   Compose: $COMPOSE_FILE"
    echo "   Environment: .env"
}

function show_help {
    echo "ERMAC Panel Management Script"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  status     - Show service status"
    echo "  start      - Start all services"
    echo "  stop       - Stop all services"
    echo "  restart    - Restart all services"
    echo "  logs       - Show logs for all services"
    echo "  logs <svc> - Show logs for specific service"
    echo "  update     - Update panel to latest version"
    echo "  backup     - Create database backup"
    echo "  info       - Show panel information"
    echo "  help       - Show this help"
    echo ""
    echo "Examples:"
    echo "  $0 status"
    echo "  $0 logs php"
    echo "  $0 restart"
    echo "  $0 backup"
}

# Main script logic
case "$1" in
    "status")
        show_status
        ;;
    "start")
        start_services
        ;;
    "stop")
        stop_services
        ;;
    "restart")
        restart_services
        ;;
    "logs")
        show_logs $2
        ;;
    "update")
        update_panel
        ;;
    "backup")
        backup_database
        ;;
    "info")
        show_info
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    "")
        echo_warning "No command specified. Use 'help' for available commands."
        show_help
        ;;
    *)
        echo_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
